#!/usr/bin/env python3
"""
Test script for transformation engine
"""

import sys
from pathlib import Path
from data_pipeline import <PERSON><PERSON>xcelRead<PERSON>, DataReader, TransformationEngine

def test_transformation():
    """Test transformation functionality"""

    # Initialize components
    excel_reader = MasterExcelReader("BRD file Automation.xlsx")
    data_reader = DataReader()
    transformation_engine = TransformationEngine()

    # Test with SR 1
    print("=== Testing Transformation for SR 1 ===")

    # Read transformation rules for SR 1
    rules = excel_reader.read_transformation_rules("1")
    print(f"Found {len(rules)} transformation rules for SR 1:")

    for i, rule in enumerate(rules[:10]):  # Show first 10 rules
        print(f"  {i+1}. {rule.header_name} - Required: {rule.required} - Action: {rule.special_action}")

    # Read a sample file (using contract.txt as example)
    print("\n=== Reading Sample Data ===")
    df = data_reader.read_file("Reference Files for Understanding/contract.txt")
    print(f"Original data shape: {df.shape}")
    print("First 3 rows of original data:")
    print(df.head(3).to_string())

    # Apply transformations
    print("\n=== Applying Transformations ===")
    df_transformed, columns_dropped, columns_transformed = transformation_engine.apply_transformations(df, rules)

    print(f"Transformed data shape: {df_transformed.shape}")
    print(f"Columns dropped: {len(columns_dropped)}")
    print(f"Columns transformed: {len(columns_transformed)}")

    if columns_dropped:
        print(f"Dropped columns: {columns_dropped[:5]}...")  # Show first 5

    if columns_transformed:
        print(f"Transformed columns: {columns_transformed}")

    print("\nFirst 3 rows of transformed data:")
    print(df_transformed.head(3).to_string())

    # Test specific transformations
    print("\n=== Testing Specific Transformations ===")

    # Find columns with date conversion
    date_columns = [rule.header_name for rule in rules if rule.required and 'date' in rule.special_action.lower()]
    if date_columns:
        print(f"Date conversion columns: {date_columns}")
        for col in date_columns[:2]:  # Test first 2 date columns
            if col in df_transformed.columns:
                print(f"Sample {col} values: {df_transformed[col].head(3).tolist()}")

    # Find columns with divide by 100
    divide_columns = [rule.header_name for rule in rules if rule.required and 'divide' in rule.special_action.lower()]
    if divide_columns:
        print(f"Divide by 100 columns: {divide_columns}")
        for col in divide_columns[:2]:  # Test first 2 divide columns
            if col in df_transformed.columns:
                print(f"Sample {col} values: {df_transformed[col].head(3).tolist()}")

if __name__ == "__main__":
    test_transformation()