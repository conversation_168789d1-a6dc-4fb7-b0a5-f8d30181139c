#!/usr/bin/env python3
"""
Graphical User Interface for the Data Processing Pipeline

This script provides a simple Tkinter GUI for running the automated data processing pipeline.
It allows users to select the master Excel file, configure output options, and run the pipeline.
"""

import os
import sys
import logging
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from datetime import datetime
from pathlib import Path
from queue import Queue
from data_pipeline import DataProcessingPipeline, FileInfo

# Custom logging handler that redirects logs to a queue
class QueueHandler(logging.Handler):
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.put(record)

# Main application class
class DataProcessingApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Data Processing Pipeline")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)

        # Set icon if available
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # Variables
        self.excel_file_var = tk.StringVar()
        self.output_dir_var = tk.StringVar(value="output")
        self.log_level_var = tk.StringVar(value="INFO")
        self.sr_filter_var = tk.StringVar()
        self.status_var = tk.StringVar(value="Ready")
        self.progress_var = tk.DoubleVar(value=0)

        # File list
        self.file_list = []
        self.selected_files = []

        # Log queue for thread-safe logging
        self.log_queue = Queue()
        self.setup_logging()

        # Create UI
        self.create_widgets()

        # Start log consumer
        self.log_consumer()

    def setup_logging(self):
        """Setup logging to redirect to the text widget"""
        # Create handler
        handler = QueueHandler(self.log_queue)
        handler.setLevel(logging.INFO)

        # Configure formatter
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)

        # Get root logger and add handler
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)

        # Remove existing handlers and add our queue handler
        for hdlr in root_logger.handlers[:]:
            root_logger.removeHandler(hdlr)

        root_logger.addHandler(handler)

    def log_consumer(self):
        """Check for log records and update the log text widget"""
        while True:
            try:
                record = self.log_queue.get(block=False)
                self.log_text.configure(state="normal")
                self.log_text.insert(tk.END, self.format_log(record) + "\n")
                self.log_text.see(tk.END)
                self.log_text.configure(state="disabled")
                self.log_queue.task_done()
            except:
                break

        # Schedule to run again
        self.root.after(100, self.log_consumer)

    def format_log(self, record):
        """Format log record for display"""
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        return formatter.format(record)

    def create_widgets(self):
        """Create all UI widgets"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Input section
        input_frame = ttk.LabelFrame(main_frame, text="Input Configuration", padding="10")
        input_frame.pack(fill=tk.X, pady=5)

        # Excel file selection
        ttk.Label(input_frame, text="Master Excel File:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(input_frame, textvariable=self.excel_file_var, width=50).grid(row=0, column=1, sticky=tk.EW, pady=5)
        ttk.Button(input_frame, text="Browse...", command=self.browse_excel).grid(row=0, column=2, padx=5, pady=5)

        # Output directory
        ttk.Label(input_frame, text="Output Directory:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(input_frame, textvariable=self.output_dir_var, width=50).grid(row=1, column=1, sticky=tk.EW, pady=5)
        ttk.Button(input_frame, text="Browse...", command=self.browse_output_dir).grid(row=1, column=2, padx=5, pady=5)

        # Log level
        ttk.Label(input_frame, text="Log Level:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Combobox(input_frame, textvariable=self.log_level_var, values=["DEBUG", "INFO", "WARNING", "ERROR"],
                    state="readonly", width=10).grid(row=2, column=1, sticky=tk.W, pady=5)

        # SR filter
        ttk.Label(input_frame, text="SR Filter (comma-separated):").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(input_frame, textvariable=self.sr_filter_var, width=50).grid(row=3, column=1, sticky=tk.EW, pady=5)

        # Action buttons
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=10)

        ttk.Button(button_frame, text="Load Files", command=self.load_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Process Selected", command=self.process_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Process All", command=self.process_all).pack(side=tk.LEFT, padx=5)

        # File list section
        file_frame = ttk.LabelFrame(main_frame, text="Files to Process", padding="10")
        file_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Create treeview for file list
        columns = ("sr", "filename", "location", "status")
        self.tree = ttk.Treeview(file_frame, columns=columns, show="headings", selectmode="extended")

        # Define headings
        self.tree.heading("sr", text="SR Number")
        self.tree.heading("filename", text="File Name")
        self.tree.heading("location", text="Location")
        self.tree.heading("status", text="Status")

        # Define columns
        self.tree.column("sr", width=80, anchor=tk.W)
        self.tree.column("filename", width=150, anchor=tk.W)
        self.tree.column("location", width=300, anchor=tk.W)
        self.tree.column("status", width=100, anchor=tk.CENTER)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(file_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)

        # Pack tree and scrollbar
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Log section
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Create log text widget
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.configure(state="disabled")

        # Status bar
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=5)

        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.RIGHT, padx=5)

        # Configure grid weights
        input_frame.columnconfigure(1, weight=1)

    def browse_excel(self):
        """Browse for Excel file"""
        filename = filedialog.askopenfilename(
            title="Select Master Excel File",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.excel_file_var.set(filename)

    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir_var.set(directory)

    def load_files(self):
        """Load files from the master Excel"""
        excel_file = self.excel_file_var.get()

        if not excel_file:
            messagebox.showerror("Error", "Please select a master Excel file")
            return

        if not Path(excel_file).exists():
            messagebox.showerror("Error", f"File not found: {excel_file}")
            return

        try:
            # Clear existing items
            for item in self.tree.get_children():
                self.tree.delete(item)

            # Update status
            self.status_var.set("Loading files...")
            self.root.update_idletasks()

            # Create pipeline
            pipeline = DataProcessingPipeline(excel_file, self.output_dir_var.get())

            # Read file list
            self.file_list = pipeline.master_excel_reader.read_file_list()

            # Filter by SR if specified
            sr_filter = self.sr_filter_var.get()
            if sr_filter:
                sr_numbers = [s.strip() for s in sr_filter.split(',')]
                self.file_list = [f for f in self.file_list if f.sr_number in sr_numbers]

            # Add to treeview
            for file_info in self.file_list:
                self.tree.insert("", tk.END, values=(
                    file_info.sr_number,
                    file_info.file_name,
                    file_info.file_location,
                    "Pending"
                ))

            # Update status
            self.status_var.set(f"Loaded {len(self.file_list)} files")

            # Log
            logging.info(f"Loaded {len(self.file_list)} files from {excel_file}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load files: {str(e)}")
            logging.error(f"Failed to load files: {str(e)}")
            self.status_var.set("Error loading files")

    def process_selected(self):
        """Process only selected files"""
        selected_items = self.tree.selection()

        if not selected_items:
            messagebox.showinfo("Info", "No files selected")
            return

        # Get selected files
        selected_indices = [self.tree.index(item) for item in selected_items]
        selected_files = [self.file_list[i] for i in selected_indices if i < len(self.file_list)]

        # Process files
        self._process_files(selected_files)

    def process_all(self):
        """Process all loaded files"""
        if not self.file_list:
            messagebox.showinfo("Info", "No files loaded")
            return

        # Process all files
        self._process_files(self.file_list)

    def _process_files(self, files):
        """Process the specified files"""
        if not files:
            return

        # Check inputs
        excel_file = self.excel_file_var.get()
        output_dir = self.output_dir_var.get()

        if not excel_file or not Path(excel_file).exists():
            messagebox.showerror("Error", "Please select a valid master Excel file")
            return

        # Create output directory if it doesn't exist
        try:
            Path(output_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            messagebox.showerror("Error", f"Cannot create output directory: {str(e)}")
            return

        # Set log level
        log_level = self.log_level_var.get()
        numeric_level = getattr(logging, log_level, logging.INFO)
        logging.getLogger().setLevel(numeric_level)

        # Confirm processing
        if not messagebox.askyesno("Confirm", f"Process {len(files)} file(s)?"):
            return

        # Start processing in a separate thread
        self.progress_var.set(0)
        self.status_var.set("Processing...")

        # Start processing thread
        threading.Thread(target=self._processing_thread, args=(excel_file, output_dir, files), daemon=True).start()

    def _processing_thread(self, excel_file, output_dir, files):
        """Thread for processing files"""
        try:
            # Create pipeline
            pipeline = DataProcessingPipeline(excel_file, output_dir)

            # Process each file
            results = []
            for i, file_info in enumerate(files):
                # Update progress
                progress = (i / len(files)) * 100
                self.progress_var.set(progress)
                self.status_var.set(f"Processing {i+1}/{len(files)}: {file_info.file_name}")

                # Update tree status
                for item in self.tree.get_children():
                    values = self.tree.item(item, "values")
                    if values[0] == file_info.sr_number and values[1] == file_info.file_name:
                        self.tree.item(item, values=(values[0], values[1], values[2], "Processing"))
                        break

                # Process file
                result = pipeline.process_single_file(file_info)
                results.append(result)

                # Update tree status
                for item in self.tree.get_children():
                    values = self.tree.item(item, "values")
                    if values[0] == file_info.sr_number and values[1] == file_info.file_name:
                        self.tree.item(item, values=(values[0], values[1], values[2], result.status))
                        break

            # Generate summary report
            pipeline.results = results
            pipeline._generate_summary_report()

            # Update progress to 100%
            self.progress_var.set(100)

            # Show summary
            success_count = len([r for r in results if r.status == "Success"])
            fail_count = len(results) - success_count

            self.status_var.set(f"Completed: {success_count} succeeded, {fail_count} failed")

            # Show message box with summary
            messagebox.showinfo("Processing Complete",
                               f"Processed {len(results)} file(s)\n"
                               f"Successful: {success_count}\n"
                               f"Failed: {fail_count}")

        except Exception as e:
            logging.error(f"Processing error: {str(e)}")
            self.status_var.set("Error during processing")
            messagebox.showerror("Error", f"Processing failed: {str(e)}")

def main():
    """Main function"""
    root = tk.Tk()
    app = DataProcessingApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()