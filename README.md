# Automated Data Processing Pipeline

A comprehensive Python application for automated data file processing based on Excel-defined transformation rules.

## Features

- **Multi-format Support**: Processes .txt, .dat, and .csv files
- **Dynamic Delimiter Detection**: Automatically detects pipe (|), comma (,), tab, semicolon, and space delimiters
- **Encoding Detection**: Handles various file encodings automatically
- **Excel-driven Configuration**: Uses master Excel workbook to define file lists and transformation rules
- **Data Transformations**: Supports date conversion, numeric operations, and custom transformations
- **Comprehensive Logging**: Detailed logging with configurable levels
- **Multiple Interfaces**: Command-line interface (CLI) and optional GUI
- **Robust Error Handling**: Graceful handling of file errors and inconsistent data
- **Output Generation**: Creates cleaned Excel files with metadata and summary reports

## Architecture

### Core Components

1. **MasterExcelReader**: Reads configuration from master Excel workbook
2. **FileDetector**: Detects file types, encodings, and delimiters
3. **DataReader**: Reads various file formats with automatic format detection
4. **TransformationEngine**: Applies data transformations based on rules
5. **OutputGenerator**: Creates formatted Excel output files
6. **DataProcessingPipeline**: Orchestrates the complete processing workflow

### Supported Transformations

- **Date Conversion**: Unix timestamps to DD-MMM-YY format
- **Numeric Operations**: Division by 100, number formatting
- **Column Filtering**: Keep only required columns based on Y/- flags
- **Header Mapping**: Apply custom headers to data columns

## Installation

### Prerequisites

- Python 3.7 or higher
- Required packages (see requirements.txt)

### Setup

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify installation**:
   ```bash
   python test_suite.py
   ```

## Usage

### Command Line Interface (CLI)

The CLI provides the most flexible way to run the pipeline:

#### Basic Usage
```bash
python cli.py -e "master_workbook.xlsx"
```

#### Advanced Options
```bash
# Custom output directory
python cli.py -e "master_workbook.xlsx" -o "custom_output/"

# Verbose logging
python cli.py -e "master_workbook.xlsx" -v

# Process specific SR numbers only
python cli.py -e "master_workbook.xlsx" --sr 1,2,10

# Dry run (preview without processing)
python cli.py -e "master_workbook.xlsx" --dry-run

# Quiet mode (errors only)
python cli.py -e "master_workbook.xlsx" -q
```

#### CLI Options
- `-e, --excel-file`: Path to master Excel workbook (required)
- `-o, --output-dir`: Output directory (default: "output")
- `-l, --log-level`: Logging level (DEBUG, INFO, WARNING, ERROR)
- `--log-file`: Custom log file path
- `-v, --verbose`: Enable verbose output
- `-q, --quiet`: Suppress output except errors
- `--dry-run`: Preview files without processing
- `--sr`: Process specific SR numbers (comma-separated)
- `--no-banner`: Suppress application banner
- `--version`: Show version information

### Graphical User Interface (GUI)

For non-technical users, launch the GUI:

```bash
python gui.py
```

The GUI provides:
- File browser for Excel workbook selection
- Output directory configuration
- SR number filtering
- Real-time progress tracking
- Live log display
- File status monitoring

### Master Excel Workbook Format

#### Sheet1: File List
| Sr. No. | File Name | File Location | ... |
|---------|-----------|---------------|-----|
| 1 | contract.txt | C:\Data\contract.txt | ... |
| 2 | security.txt | C:\Data\security.txt | ... |

#### SR Sheets (e.g., "Sr. No. 1"):
| Header Name | Required Y/N | Format | Special Action | Example |
|-------------|--------------|--------|----------------|---------|
| Token | Y | Text | - | 35000 |
| expiry_date | Y | Date | Convert to date | 01-AUG-15 |
| strike_price | Y | Number | Divide by 100 | 32000.00 |
| unused_field | - | Text | - | (dropped) |

## Output

### Generated Files

1. **Cleaned Data Files**: `{filename}_cleaned_{timestamp}.xlsx`
   - **Data Sheet**: Transformed data with applied rules
   - **Metadata Sheet**: Processing information and statistics

2. **Summary Report**: `processing_summary_{timestamp}.xlsx`
   - **Processing Results**: Status of each file
   - **Summary Statistics**: Success rates and error counts

### Log Files

- **Console Output**: Real-time processing information
- **Log Files**: Detailed logs saved to `pipeline_{timestamp}.log`

## Configuration

### Transformation Rules

#### Special Actions
- `Convert to date`: Unix timestamp → DD-MMM-YY format
- `Divide by 100`: Numeric division for price/amount fields
- `-`: No transformation applied

#### Required Flags
- `Y`: Include column in output
- `-`: Exclude column from output

### File Support

#### Supported Formats
- `.txt` files (pipe, tab, or space delimited)
- `.dat` files (various delimiters)
- `.csv` files (comma delimited)

#### Encoding Support
- UTF-8, ASCII, Latin-1, CP1252
- Automatic encoding detection with fallback options

## Testing

Run the comprehensive test suite:

```bash
python test_suite.py
```

Tests cover:
- File type and delimiter detection
- Data reading with various formats
- Transformation engine functionality
- Error handling scenarios

## Building Executables

### Using PyInstaller

1. **Install PyInstaller**:
   ```bash
   pip install pyinstaller
   ```

2. **Build CLI executable**:
   ```bash
   pyinstaller --onefile --console cli.py --name data_processor_cli
   ```

3. **Build GUI executable**:
   ```bash
   pyinstaller --onefile --windowed gui.py --name data_processor_gui
   ```

4. **Using spec file** (recommended):
   ```bash
   pyinstaller build_exe.spec
   ```

### Distribution

The built executables will be in the `dist/` directory and can be distributed to users without Python installation.