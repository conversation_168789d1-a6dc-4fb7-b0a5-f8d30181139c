#!/usr/bin/env python3
"""
Comprehensive Test Suite for the Data Processing Pipeline

This script provides unit tests and integration tests for all components
of the automated data processing pipeline.
"""

import unittest
import tempfile
import shutil
import pandas as pd
from pathlib import Path
from datetime import datetime
from data_pipeline import (
    MasterExcelReader, FileDetector, DataReader, TransformationEngine,
    OutputGenerator, DataProcessingPipeline, FileInfo, TransformationRule
)

class TestFileDetector(unittest.TestCase):
    """Test file detection functionality"""

    def setUp(self):
        self.detector = FileDetector()
        self.temp_dir = Path(tempfile.mkdtemp())

    def tearDown(self):
        shutil.rmtree(self.temp_dir)

    def test_file_type_detection(self):
        """Test file type detection"""
        # Test various file extensions
        test_cases = [
            ("test.txt", "TXT"),
            ("test.dat", "DAT"),
            ("test.csv", "CSV"),
            ("test.unknown", "TXT")  # Default to TXT
        ]

        for filename, expected_type in test_cases:
            file_path = self.temp_dir / filename
            file_path.touch()

            detected_type = self.detector.detect_file_type(file_path)
            self.assertEqual(detected_type.value.upper(), expected_type)

    def test_delimiter_detection(self):
        """Test delimiter detection"""
        # Create test files with different delimiters
        test_cases = [
            ("pipe_file.txt", "A|B|C\n1|2|3\n4|5|6", "|"),
            ("comma_file.csv", "A,B,C\n1,2,3\n4,5,6", ","),
            ("tab_file.txt", "A\tB\tC\n1\t2\t3\n4\t5\t6", "\t"),
        ]

        for filename, content, expected_delimiter in test_cases:
            file_path = self.temp_dir / filename
            file_path.write_text(content, encoding='utf-8')

            detected_delimiter = self.detector.detect_delimiter(file_path, 'utf-8')
            self.assertEqual(detected_delimiter, expected_delimiter)

    def test_encoding_detection(self):
        """Test encoding detection"""
        # Create test file with UTF-8 content
        test_file = self.temp_dir / "test_encoding.txt"
        test_file.write_text("Hello, World! 测试", encoding='utf-8')

        detected_encoding = self.detector.detect_encoding(test_file)
        self.assertIn(detected_encoding.lower(), ['utf-8', 'ascii'])

class TestDataReader(unittest.TestCase):
    """Test data reading functionality"""

    def setUp(self):
        self.reader = DataReader()
        self.temp_dir = Path(tempfile.mkdtemp())

    def tearDown(self):
        shutil.rmtree(self.temp_dir)

    def test_read_csv_file(self):
        """Test reading CSV files"""
        # Create test CSV file
        test_file = self.temp_dir / "test.csv"
        test_content = "A,B,C\n1,2,3\n4,5,6\n7,8,9"
        test_file.write_text(test_content, encoding='utf-8')

        # Read file
        df = self.reader.read_file(str(test_file))

        # Verify results
        self.assertEqual(df.shape, (4, 3))  # 4 rows, 3 columns
        self.assertEqual(df.iloc[0, 0], "A")  # Header
        self.assertEqual(df.iloc[1, 0], "1")  # First data row

    def test_read_pipe_delimited_file(self):
        """Test reading pipe-delimited files"""
        # Create test pipe-delimited file
        test_file = self.temp_dir / "test.txt"
        test_content = "A|B|C\n1|2|3\n4|5|6"
        test_file.write_text(test_content, encoding='utf-8')

        # Read file
        df = self.reader.read_file(str(test_file))

        # Verify results
        self.assertEqual(df.shape, (3, 3))
        self.assertEqual(df.iloc[0, 0], "A")
        self.assertEqual(df.iloc[1, 1], "2")

    def test_read_inconsistent_columns(self):
        """Test reading files with inconsistent column counts"""
        # Create test file with inconsistent columns
        test_file = self.temp_dir / "test_inconsistent.txt"
        test_content = "A|B\n1|2|3|4\n5|6"  # Different column counts
        test_file.write_text(test_content, encoding='utf-8')

        # Read file (should handle inconsistent columns)
        df = self.reader.read_file(str(test_file))

        # Verify results (should pad to max columns)
        self.assertEqual(df.shape[1], 4)  # Should have 4 columns (max from any row)

class TestTransformationEngine(unittest.TestCase):
    """Test transformation functionality"""

    def setUp(self):
        self.engine = TransformationEngine()

    def test_date_conversion(self):
        """Test Unix timestamp to date conversion"""
        # Test data with Unix timestamps
        test_series = pd.Series(['1438439400', '1430265600', '0', '', 'XX'])

        # Apply conversion
        converted = self.engine._convert_unix_to_date(test_series)

        # Verify results
        self.assertEqual(converted.iloc[0], '01-AUG-15')  # 1438439400 -> Aug 1, 2015
        self.assertEqual(converted.iloc[1], '29-APR-15')  # 1430265600 -> Apr 29, 2015
        self.assertEqual(converted.iloc[2], '')           # 0 -> empty
        self.assertEqual(converted.iloc[3], '')           # empty -> empty
        self.assertEqual(converted.iloc[4], 'XX')         # non-numeric -> unchanged

    def test_divide_by_100(self):
        """Test divide by 100 transformation"""
        # Test data
        test_series = pd.Series(['3200000', '100', '0', '', 'ABC'])

        # Apply transformation
        converted = self.engine._divide_by_100(test_series)

        # Verify results
        self.assertEqual(converted.iloc[0], '32000')      # 3200000 / 100
        self.assertEqual(converted.iloc[1], '1')          # 100 / 100
        self.assertEqual(converted.iloc[2], '0')          # 0 / 100
        self.assertEqual(converted.iloc[3], '')           # empty -> empty
        self.assertEqual(converted.iloc[4], 'ABC')        # non-numeric -> unchanged

def run_tests():
    """Run all tests"""
    # Create test suite
    test_suite = unittest.TestSuite()

    # Add test classes
    test_classes = [
        TestFileDetector,
        TestDataReader,
        TestTransformationEngine,
    ]

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # Return success status
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)