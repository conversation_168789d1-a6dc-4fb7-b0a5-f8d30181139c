#!/usr/bin/env python3
"""
Test script for file detection and reading functionality
"""

import sys
from pathlib import Path
from data_pipeline import <PERSON>Reader, FileDetector

def test_file_detection():
    """Test file detection functionality"""

    detector = FileDetector()
    reader = DataReader()

    # Test with the reference files
    test_files = [
        'Reference Files for Understanding/contract.txt',
        'Reference Files for Understanding/security.txt',
        'Reference Files for Understanding/BSE_EQD_CONTRACT_02072025.csv'
    ]

    for file_path in test_files:
        if Path(file_path).exists():
            print(f'\n=== Testing {file_path} ===')

            # Test file type detection
            file_type = detector.detect_file_type(Path(file_path))
            print(f'File type: {file_type}')

            # Test encoding detection
            encoding = detector.detect_encoding(Path(file_path))
            print(f'Encoding: {encoding}')

            # Test delimiter detection
            delimiter = detector.detect_delimiter(Path(file_path), encoding)
            print(f'Delimiter: "{delimiter}"')

            # Test reading first few rows
            try:
                df = reader.read_file(file_path)
                print(f'Shape: {df.shape}')
                print('First 3 rows:')
                print(df.head(3).to_string())
            except Exception as e:
                print(f'Error reading file: {e}')
        else:
            print(f'File not found: {file_path}')

if __name__ == "__main__":
    test_file_detection()