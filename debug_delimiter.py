#!/usr/bin/env python3
"""Debug delimiter detection"""

from pathlib import Path

def debug_delimiter_detection():
    file_path = Path('Reference Files for Understanding/contract.txt')

    with open(file_path, 'r', encoding='ascii') as f:
        sample_lines = []
        for i, line in enumerate(f):
            if i >= 5:
                break
            sample_lines.append(line.strip())

    print("Sample lines:")
    for i, line in enumerate(sample_lines):
        print(f"{i}: {line[:100]}...")

    print("\nDelimiter analysis:")
    delimiters = ['|', ',', '\t', ';', ' ']

    for delimiter in delimiters:
        scores = []
        for line in sample_lines:
            if line:
                parts = line.split(delimiter)
                scores.append(len(parts))

        if scores:
            avg_cols = sum(scores) / len(scores)
            max_cols = max(scores)
            min_cols = min(scores)
            print(f"Delimiter '{delimiter}': avg={avg_cols:.1f}, max={max_cols}, min={min_cols}, scores={scores}")

if __name__ == "__main__":
    debug_delimiter_detection()